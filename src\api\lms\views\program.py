from rest_framework import viewsets
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated
from services.google.classroom import GoogleClassroomManager
from core.models import (
    Offering,
    StudentEnrollment,
    User,
    OrderItem,
    Order,
)
from api.mixins import AuditMixin, SwaggerTagMixin
from api.permissions import IsStaffUser

from api.lms.serializers.program import (
    LmsProgramSerializer,
    LmsTeamChannelSerializer,
    LmsProgramEnrollmentRetrieveSerializer,
    LmsRetrieveProgramSerializer,
    LmsUpdateProgramSerializer,
)
from api.paginations import StandardResultsPagination
from rest_framework.response import Response
from rest_framework.decorators import action
from django.conf import settings
from api.crm.services.evolution_api import EvolutionAPIClient
from services.cache.redis import CacheManager
from rest_framework import status

import logging

logger = logging.getLogger(__name__)


class LmsProgramViewSet(
    AuditMixin,
    SwaggerTagMixin,
    viewsets.ModelViewSet,
):
    swagger_tags = ["Programs"]

    queryset = Offering.objects.filter(
        stage__in=[Offering.ENROLLMENT_STAGE, Offering.ENROLLMENT_CLOSED_STAGE],
        deleted=False,
    ).order_by("-created_at")
    serializer_class = LmsProgramSerializer
    pagination_class = StandardResultsPagination

    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated, IsStaffUser]

    def get_serializer_class(self):
        if self.action == "retrieve":
            return LmsRetrieveProgramSerializer
        if self.action == "update":
            return LmsUpdateProgramSerializer
        if self.action == "partial_update":
            return LmsUpdateProgramSerializer
        return super().get_serializer_class()

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.instance_name = settings.EVOLUTION_API_INSTANCE_NAME
        self.evo_client = EvolutionAPIClient(self.instance_name)

        self.cache_manager = CacheManager("lms_programs")

    def _is_evo_service_available(self):
        try:
            info = self.evo_client.info.get_info()
            return info["data"].get("status", None) == 200
        except Exception as e:
            logger.error(f"Error al verificar el estado del servicio de Evolution: {e}")
            return False

    """
    List enrollments in the external classroom system
    """

    @action(
        detail=True,
        methods=["get"],
        url_path="enrollments",
        serializer_class=LmsProgramEnrollmentRetrieveSerializer,
    )
    def list_enrollments(self, request, pk=None):
        try:
            offering = self.get_object()

            # Enrollments en nuestro sistema para este offering (programa)
            enrollments = (
                StudentEnrollment.objects.filter(
                    order_item__offering=offering, deleted=False
                )
                .select_related("user", "order_item")
                .order_by("user__last_name", "user__first_name", "-created_at")
            )

            error_message = None
            if not offering.ext_reference:
                error_message = (
                    "No se ha establecido el código de curso para este programa."
                )
                return self._build_response_without_classroom(
                    offering, enrollments, error_message
                )

            classroom_data, classroom_error = self._get_classroom_data_with_cache(
                offering.ext_reference
            )

            if classroom_data is None:
                error_message = (
                    "Ocurrió un error al obtener los datos del curso en classroom, verifique que el código de curso sea correcto. Si el problema persiste, contacte con el administrador del sistema."
                    if not classroom_error
                    else classroom_error
                )

                return self._build_response_without_classroom(
                    offering, enrollments, error_message
                )

            result = self._process_enrollments_with_classroom(
                offering, enrollments, classroom_data
            )

            serializer = LmsProgramEnrollmentRetrieveSerializer(result)
            return Response(serializer.data, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error al obtener enrollments: {e}")
            return Response(
                {
                    "message": "Ocurrió un error al obtener los enrollments",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    def _build_response_without_classroom(
        self, offering: Offering, enrollments, error_message=None
    ):
        enrollments_list = list(enrollments)

        for enrollment in enrollments_list:
            enrollment.accepted_invitation = None
            enrollment.needs_conciliation = False
            enrollment.full_name = None
            enrollment.email = None

            if not enrollment.order_item and enrollment.user:
                # buscar order_item vendido por offering y user
                enrollment.order_item = OrderItem.objects.filter(
                    offering=offering,
                    user=enrollment.user,
                    order__stage=Order.SOLD_STAGE,
                ).first()

        data = {
            "enrollments": enrollments_list,
            "enrollments_count": len(enrollments_list),
            "accepted_invitation_count": None,
            "needs_conciliation_count": None,
            "error_message": error_message,
        }
        serializer = LmsProgramEnrollmentRetrieveSerializer(data)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def _get_classroom_data_with_cache(self, ext_reference):
        cache_key = f"classroom_enrollments_{ext_reference}"
        self.cache_manager.invalidate_specific(cache_key)
        classroom_students = self.cache_manager.get(cache_key)

        error_message = None

        if classroom_students is None:
            try:
                classroom_instance = GoogleClassroomManager()
                course = classroom_instance.get_course_by_enrollment_code(ext_reference)

                if course:
                    classroom_students = classroom_instance.list_course_students(
                        course.get("id")
                    )
                    # Cache por 3 minutos
                    self.cache_manager.set(cache_key, classroom_students, timeout=180)
                else:
                    classroom_students = None
                    error_message = f"No se ha encontrado el curso con código {ext_reference} en Classroom"

            except Exception as e:
                logger.error(f"Error al obtener estudiantes de classroom: {e}")
                classroom_students = None
                error_message = f"Ocurrió un error inesperado al obtener los estudiantes de classroom. Por favor, inténtelo de nuevo más tarde."

        return classroom_students, error_message

    def _process_enrollments_with_classroom(
        self, offering: Offering, enrollments, classroom_students
    ):
        classroom_students_map = {
            student.get("profile", {}).get("emailAddress", "").lower(): student
            for student in classroom_students
            if student.get("profile", {}).get("emailAddress")
        }

        classroom_emails = set(classroom_students_map.keys())
        enrollment_emails = set()
        accepted_count = 0
        all_enrollments = []

        for enrollment in enrollments:
            user_email = enrollment.user.email.lower() if enrollment.user.email else ""
            enrollment_emails.add(user_email)

            enrollment.accepted_invitation = user_email in classroom_emails
            enrollment.needs_conciliation = False
            enrollment.full_name = None
            enrollment.email = None

            if enrollment.accepted_invitation:
                accepted_count += 1

            all_enrollments.append(enrollment)

        emails_needing_conciliation = classroom_emails - enrollment_emails

        if emails_needing_conciliation:
            existing_users = {
                user.email.lower(): user
                for user in User.objects.filter(
                    email__in=list(emails_needing_conciliation)
                ).only("uid", "email")
            }

            for email in emails_needing_conciliation:
                if email in existing_users:
                    student_data = classroom_students_map[email]
                    profile = student_data.get("profile", {})

                    mock_enrollment = self._create_mock_enrollment(
                        offering, existing_users[email], profile
                    )

                    # cuenta como invitación aceptada si ya está registrado en classroom
                    if mock_enrollment.accepted_invitation:
                        accepted_count += 1

                    all_enrollments.append(mock_enrollment)

        return {
            "enrollments": all_enrollments,
            "enrollments_count": len(all_enrollments),
            "accepted_invitation_count": accepted_count,
            "needs_conciliation_count": len(emails_needing_conciliation),
            "error_message": None,
        }

    def _create_mock_enrollment(self, offering, user, profile):
        from types import SimpleNamespace

        order_item = None
        accepted_invitation = False
        if user:
            order_item = OrderItem.objects.filter(
                offering=offering,
                order__owner=user,
                order__stage=Order.SOLD_STAGE,
            ).first()
            if order_item:
                accepted_invitation = (
                    order_item.ext_invitation_status == OrderItem.INVITATION_SENT
                )

        return SimpleNamespace(
            eid=None,
            user=user,
            order_item=order_item,
            accepted_invitation=accepted_invitation,
            needs_conciliation=True,
            full_name=profile.get("name", {}).get("fullName", ""),
            email=profile.get("emailAddress", ""),
        )

    """
    Get channel from offering team_channel_id, currently using Evolution API for WhatsApp Groups
    """

    @action(
        detail=True,
        methods=["get"],
        url_path="team-channel",
        serializer_class=LmsTeamChannelSerializer,
    )
    def get_team_channel(self, request, pk=None):
        try:
            offering = self.get_object()

            if not offering.team_channel_id:
                return Response(
                    {
                        "message": "Este curso no tiene un grupo asignado.",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            if not self._is_evo_service_available():
                return Response(
                    {
                        "message": "El servicio de WhatsApp no se encuentra disponible en este momento.",
                    },
                    status=status.HTTP_424_FAILED_DEPENDENCY,
                )

            response = self.evo_client.groups.fetch_by_jid(offering.team_channel_id)
            group_data = response.get("data")

            mapped_group = {
                "id": group_data.get("id"),
                "subject": group_data.get("subject", None),
                "subject_time": group_data.get("subjectTime", None),
                "description": group_data.get("desc", None),
                "picture_url": group_data.get("pictureUrl", None),
                "size": group_data.get("size", None),
                "creation": group_data.get("creation", None),
            }

            return Response(
                LmsTeamChannelSerializer(mapped_group).data,
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            logger.error(f"Error al obtener el canal de comunicación: {e}")
            return Response(
                {
                    "message": "Ocurrió un error al obtener el canal de comunicación",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
