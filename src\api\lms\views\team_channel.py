from rest_framework import viewsets, status
from rest_framework.response import Response
from django.conf import settings
from api.mixins import AuditMixin, SwaggerTagMixin
from api.lms.serializers.program import LmsTeamChannelSerializer
from api.paginations import StandardResultsPagination
from api.crm.services.evolution_api import EvolutionAP<PERSON><PERSON>, EvolutionAPIError
from services.cache.redis import Cache<PERSON>anager
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated
from api.permissions import IsStaffUser
from api.automations.utils.text_processing import normalize_text

import logging

logger = logging.getLogger(__name__)


class TeamChannelViewSet(
    AuditMixin,
    SwaggerTagMixin,
    viewsets.ReadOnlyModelViewSet,
):
    """
    ViewSet for managing team channels (WhatsApp groups) from Evolution API.
    Only supports GET operations with pagination and search functionality.
    """

    swagger_tags = ["Team Channels"]
    serializer_class = LmsTeamChannelSerializer
    pagination_class = StandardResultsPagination

    authentication_classes = [TokenAuthentication]
    permission_classes = [Is<PERSON><PERSON><PERSON>icated, IsStaffUser]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.instance_name = settings.EVOLUTION_API_INSTANCE_NAME
        self.evo_client = EvolutionAPIClient(self.instance_name)
        self.cache_manager = CacheManager("team_channels")
        self.cache_timeout = 60 * 15  # 15 minutes

    def _is_evo_service_available(self):
        """Check if Evolution API service is available."""
        try:
            info = self.evo_client.info.get_info()
            return info["data"].get("status", None) == 200
        except Exception as e:
            logger.error(f"Error al verificar el estado del servicio de Evolution: {e}")
            return False

    def _get_all_team_channels(self):
        """Fetch all team channels from Evolution API with caching."""
        cache_key = "team_channels"
        cached_data = self.cache_manager.get(cache_key)

        if cached_data:
            return cached_data

        if not self._is_evo_service_available():
            raise EvolutionAPIError(
                "El servicio de WhatsApp no se encuentra disponible en este momento."
            )

        response = self.evo_client.groups.fetch_all()

        # Map team channels response to use in serializer
        mapping = {
            "id": "id",
            "subject": "subject",
            "subjectTime": "subject_time",
            "desc": "description",
            "pictureUrl": "picture_url",
            "size": "size",
            "creation": "creation",
        }

        response_data = response.get("data", [])
        mapped_response = [
            {mapping.get(key, key): value for key, value in group.items()}
            for group in response_data
        ]

        # Cache the results
        self.cache_manager.set(cache_key, mapped_response, timeout=self.cache_timeout)

        return mapped_response

    def _filter_by_search(self, queryset, search_term):
        """Filter team channels by subject (case insensitive)."""
        if not search_term:
            return queryset

        search_term = normalize_text(search_term)

        return [
            channel
            for channel in queryset
            if search_term in normalize_text(channel.get("subject", ""))
        ]

    def get_queryset(self):
        """
        Override get_queryset to return filtered team channels data.
        This method is called by the pagination system.
        """
        try:
            # Get all team channels
            team_channels = self._get_all_team_channels()

            # Apply search filter if provided
            search = self.request.query_params.get("search", None)
            if search:
                team_channels = self._filter_by_search(team_channels, search)

            return team_channels

        except EvolutionAPIError as e:
            logger.error(f"Evolution API Error: {e}")
            return []
        except Exception as e:
            logger.error(f"Error al obtener los canales disponibles: {e}")
            return []
