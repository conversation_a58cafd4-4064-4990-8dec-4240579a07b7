import logging
from celery import shared_task
from django.conf import settings
from api.crm.services.evolution_api import EvolutionAPIClient, EvolutionAPIError
from services.cache.redis import CacheManager

logger = logging.getLogger(__name__)

# Use this constant to fetch the team channels from the cache in other functions
TEAM_CHANNELS_PREFIX = "ceu_team_channels"
TEAM_CHANNELS_CACHE_KEY = "all"


@shared_task(bind=True)
def fetch_ceu_team_channels(self):
    """
    Fetches the CEU team channels from Evolution API and stores them in the redis cache.
    This task runs every 15 minutes to keep the cache updated.
    """

    try:
        logger.info("Starting team channels fetch task")

        # Initialize Evolution API client
        instance_name = settings.EVOLUTION_API_INSTANCE_NAME
        evo_client = EvolutionAPIClient(instance_name)

        # Initialize cache manager with the same prefix used by ViewSet
        cache_manager = CacheManager(TEAM_CHANNELS_PREFIX)

        # Check if Evolution API service is available
        try:
            info = evo_client.info.get_info()
            if info["data"].get("status", None) != 200:
                logger.error("Evolution API service is not available")
                return {
                    "success": False,
                    "message": "Evolution API service unavailable",
                }
        except Exception as e:
            logger.error(f"Error checking Evolution API service status: {e}")
            return {"success": False, "message": f"Service check failed: {str(e)}"}

        # Fetch all groups from Evolution API
        logger.info(f"Fetching all groups from Evolution API instance: {instance_name}")
        response = evo_client.groups.fetch_all()

        # Map team channels response to use in serializer (same mapping as ViewSet)
        mapping = {
            "id": "id",
            "subject": "subject",
            "subjectTime": "subject_time",
            "desc": "description",
            "pictureUrl": "picture_url",
            "size": "size",
            "creation": "creation",
        }

        response_data = response.get("data", [])
        mapped_response = [
            {mapping.get(key, key): value for key, value in group.items()}
            for group in response_data
        ]

        # Cache the results with 15 minutes timeout (same as task frequency)
        cache_timeout = 60 * 15  # 15 minutes
        cache_manager.set(
            TEAM_CHANNELS_CACHE_KEY, mapped_response, timeout=cache_timeout
        )

        logger.info(f"Successfully cached {len(mapped_response)} team channels")
        return {
            "success": True,
            "message": f"Cached {len(mapped_response)} team channels",
            "count": len(mapped_response),
        }

    except EvolutionAPIError as e:
        logger.error(f"Evolution API Error in team channels fetch: {e}")
        return {"success": False, "message": f"Evolution API Error: {str(e)}"}
    except Exception as e:
        logger.error(f"Unexpected error fetching CEU team channels: {e}")
        # Retry with exponential backoff
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60 * (2**self.request.retries))
        return {"success": False, "message": f"Unexpected error: {str(e)}"}
