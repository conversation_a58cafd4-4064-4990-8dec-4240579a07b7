import logging
from celery import shared_task

logger = logging.getLogger(__name__)

# Use this constant to fetch the team channels from the cache in other functions
TEAM_CHANNELS_PREFIX = "ceu_team_channels"
TEAM_CHANNELS_CACHE_KEY = "all"


@shared_task(bind=True)
def fetch_ceu_team_channels(self):
    """
    Fetches the CEU team channels and stores then in the redis cache.
    """

    try:
        pass
    except Exception as e:
        logger.error(f"Error fetching CEU team channels: {e}")
        raise
