from celery.schedules import crontab

CELERYBEAT_SCHEDULE = {
    "schedule_pending_invitations": {
        "task": "api.crm.tasks.event_invitations.core.schedule_pending_invitations",
        "schedule": crontab(minute="*/2"),  # Every 2 minutes
    },
    "fetch_ceu_team_channels": {
        "task": "api.lms.tasks.team_channel.fetch_ceu_team_channels",
        "schedule": crontab(minute="*/15"),  # Every 15 minutes
    },
}
